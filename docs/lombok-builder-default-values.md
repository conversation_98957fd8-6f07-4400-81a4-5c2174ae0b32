# Lombok Builder 默认值问题解决方案

## 问题描述

在使用 Lombok 的 `@Builder` 注解时，字段的默认值不会生效：

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenAiRequestLogInfo {
    
    private String provider = "DEFAULT";  // ❌ 默认值在Builder中不生效
    
    // 其他字段...
}

// 使用Builder创建对象
public OpenAiRequestLogInfo createRequestLog() {
    return OpenAiRequestLogInfo.builder()
            .requestId(generateRequestId())
            .startTime(LocalDateTime.now())
            .retryCount(0)
            .build();  // provider 字段为 null，而不是 "DEFAULT"
}
```

## 根本原因分析

### 1. Lombok Builder 的工作机制

Lombok 生成的 Builder 类不会使用原始类的字段默认值：

```java
// Lombok 生成的 Builder 类（简化版）
public static class OpenAiRequestLogInfoBuilder {
    private String provider;  // 注意：这里没有默认值！
    private String requestId;
    private LocalDateTime startTime;
    private Integer retryCount;
    
    public OpenAiRequestLogInfoBuilder provider(String provider) {
        this.provider = provider;
        return this;
    }
    
    public OpenAiRequestLogInfo build() {
        OpenAiRequestLogInfo result = new OpenAiRequestLogInfo();
        result.provider = this.provider;      // 如果没设置，就是 null
        result.requestId = this.requestId;
        result.startTime = this.startTime;
        result.retryCount = this.retryCount;
        return result;
    }
}
```

### 2. 为什么默认值失效

1. **Builder 模式的设计**：Builder 类有自己的字段，这些字段默认为 null
2. **构建过程**：`build()` 方法直接将 Builder 的字段值赋给目标对象
3. **跳过构造函数**：Builder 不会调用带默认值的构造函数

## 解决方案

### 方案1：使用 `@Builder.Default` 注解（推荐）

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenAiRequestLogInfo {
    
    /**
     * AI提供商
     */
    @Builder.Default  // ✅ 告诉 Builder 使用这个默认值
    private String provider = "DEFAULT";
    
    @Builder.Default
    private String status = "PENDING";
    
    @Builder.Default
    private Integer retryCount = 0;
    
    // 其他字段...
}
```

**生成的 Builder 代码**：
```java
public static class OpenAiRequestLogInfoBuilder {
    private String provider = "DEFAULT";  // ✅ 现在有默认值了
    private String status = "PENDING";
    private Integer retryCount = 0;
    
    // 其他方法...
}
```

### 方案2：在 Builder 调用中显式设置

```java
public OpenAiRequestLogInfo createRequestLog() {
    return OpenAiRequestLogInfo.builder()
            .requestId(generateRequestId())
            .startTime(LocalDateTime.now())
            .retryCount(0)
            .provider("DEFAULT")  // ✅ 显式设置默认值
            .status("PENDING")    // ✅ 显式设置默认值
            .build();
}
```

### 方案3：使用工厂方法

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenAiRequestLogInfo {
    
    private String provider;
    private String status;
    private Integer retryCount;
    
    // 工厂方法
    public static OpenAiRequestLogInfo createDefault() {
        return OpenAiRequestLogInfo.builder()
                .provider("DEFAULT")
                .status("PENDING")
                .retryCount(0)
                .build();
    }
    
    // 带参数的工厂方法
    public static OpenAiRequestLogInfo createWithRequestId(String requestId) {
        return createDefault()
                .toBuilder()  // 基于默认值创建新的 Builder
                .requestId(requestId)
                .startTime(LocalDateTime.now())
                .build();
    }
}
```

### 方案4：使用 `@Value.Default`（Immutables 库）

如果使用 Immutables 库而不是 Lombok：

```java
@Value.Immutable
public interface OpenAiRequestLogInfo {
    
    @Value.Default
    default String provider() {
        return "DEFAULT";
    }
    
    @Value.Default
    default String status() {
        return "PENDING";
    }
    
    // 其他字段...
}
```

## 最佳实践

### 1. 推荐使用 `@Builder.Default`

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenAiRequestLogInfo {
    
    // 对于有默认值的字段，使用 @Builder.Default
    @Builder.Default
    private String provider = "DEFAULT";
    
    @Builder.Default
    private String status = "PENDING";
    
    @Builder.Default
    private Integer retryCount = 0;
    
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    
    // 对于必须设置的字段，不使用默认值
    private String requestId;
    private Long userId;
    private String modelName;
}
```

### 2. 常见的默认值场景

```java
@Builder.Default
private String status = "PENDING";

@Builder.Default
private Integer retryCount = 0;

@Builder.Default
private Boolean isActive = true;

@Builder.Default
private List<String> tags = new ArrayList<>();

@Builder.Default
private Map<String, Object> metadata = new HashMap<>();

@Builder.Default
private LocalDateTime createdAt = LocalDateTime.now();
```

### 3. 注意事项

1. **可变对象的默认值**：
   ```java
   // ❌ 错误：所有实例共享同一个 List
   @Builder.Default
   private List<String> tags = new ArrayList<>();
   
   // ✅ 正确：每个实例有独立的 List
   @Builder.Default
   private List<String> tags = new ArrayList<>();
   ```

2. **时间相关的默认值**：
   ```java
   // ❌ 错误：所有实例使用相同的时间（类加载时间）
   @Builder.Default
   private LocalDateTime createdAt = LocalDateTime.now();
   
   // ✅ 正确：使用方法引用或 Supplier
   @Builder.Default
   private LocalDateTime createdAt = LocalDateTime.now();  // 实际上这个是正确的
   ```

## 验证修复效果

### 修复前

```java
OpenAiRequestLogInfo logInfo = OpenAiRequestLogInfo.builder()
        .requestId("req_12345")
        .build();

System.out.println(logInfo.getProvider());  // 输出：null
```

### 修复后

```java
OpenAiRequestLogInfo logInfo = OpenAiRequestLogInfo.builder()
        .requestId("req_12345")
        .build();

System.out.println(logInfo.getProvider());  // 输出：DEFAULT
```

## 相关文件

- `src/main/java/com/example/pure/model/dto/response/openai/OpenAiRequestLogInfo.java`
- `src/main/java/com/example/pure/service/openai/OpenAiRequestLogService.java`

## 总结

使用 `@Builder.Default` 注解是解决 Lombok Builder 默认值问题的最佳方案：

1. **简单易用**：只需要添加一个注解
2. **类型安全**：编译时检查
3. **性能良好**：没有额外的运行时开销
4. **可读性强**：默认值在字段定义处清晰可见

这个修复确保了 Builder 模式下的默认值能够正常工作，提高了代码的健壮性和可维护性。
