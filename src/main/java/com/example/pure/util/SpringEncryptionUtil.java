package com.example.pure.util;


import com.example.pure.mapper.primary.CompatibleApiKeyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.encrypt.BytesEncryptor;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import org.springframework.security.crypto.keygen.KeyGenerators;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Spring加密工具类
 * <p>
 * 使用Spring Security的加密器进行API密钥加密和解密
 * </p>
 */
@Slf4j
@Component
public class SpringEncryptionUtil {

    @Value("${app.encryption.password:defaultPassword123}")
    private String encryptionPassword; // 加密器的密码加salt就能生成密钥来加密或解密

    @Value("${app.encryption.salt:64656661756c7453616c74343536}") // salt就是计算哈希值前给原始密码添加的额外密码值,为了让哈希值更安全
    private String encryptionSalt;                  // 例如12345678abc的计算哈希的值和12345678加salt值abc计算的哈希相等

    private TextEncryptor textEncryptor;           // 现有API密钥加密 (CBC)
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private CompatibleApiKeyMapper compatibleApiKeyMapper;

    public SpringEncryptionUtil(SnowflakeIdGenerator snowflakeIdGenerator) {
        this.snowflakeIdGenerator = snowflakeIdGenerator;
    }

    @PostConstruct
    public void init() {
        try {
            // 现有的CBC模式加密器（向后兼容）
            this.textEncryptor = Encryptors.text(encryptionPassword, encryptionSalt);

            log.info("Spring加密工具初始化成功（CBC模式）");
        } catch (Exception e) {
            log.error("Spring加密工具初始化失败", e);
            throw new RuntimeException("加密工具初始化失败", e);
        }
    }

    /**
     * 生成随机盐值
     * 每次调用都生成不同的随机盐值，用于独立的加密操作
     */
    private String generateRandomSalt() {
        return KeyGenerators.string().generateKey();
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 加密API密钥
     *
     * @param apiKey 原始API密钥
     * @return 加密后的API密钥
     */
    public String encrypt(String apiKey) {
        try {
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("API密钥不能为空");
            }
            return textEncryptor.encrypt(apiKey);
        } catch (Exception e) {
            log.error("加密API密钥失败", e);
            throw new RuntimeException("加密失败", e);
        }
    }

    /**
     * 解密API密钥
     *
     * @param encryptedApiKey 加密后的API密钥
     * @return 原始API密钥
     */
    public String decrypt(String encryptedApiKey) {
        try {
            if (encryptedApiKey == null || encryptedApiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("加密的API密钥不能为空");
            }

            // 验证是否为有效的十六进制格式
            if (!isValidHexString(encryptedApiKey)) {
                log.error("加密数据不是有效的十六进制格式 - 长度: {}, 前20字符: {}",
                         encryptedApiKey.length(),
                         encryptedApiKey.substring(0, Math.min(20, encryptedApiKey.length())));
                throw new IllegalArgumentException("加密数据格式无效：不是有效的十六进制格式");
            }

            return textEncryptor.decrypt(encryptedApiKey);
        } catch (IllegalArgumentException e) {
            // 重新抛出参数异常，保持原始错误信息
            throw e;
        } catch (Exception e) {
            log.error("解密API密钥失败 - 数据长度: {}, 错误类型: {}, 错误信息: {}",
                     encryptedApiKey != null ? encryptedApiKey.length() : 0,
                     e.getClass().getSimpleName(),
                     e.getMessage());
            throw new RuntimeException("解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证字符串是否为有效的十六进制格式
     */
    private boolean isValidHexString(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        // 十六进制字符串长度必须是偶数
        if (str.length() % 2 != 0) {
            return false;
        }

        // 检查是否只包含十六进制字符
        return str.matches("^[0-9a-fA-F]+$");
    }



    /**
     * 生成OpenAI格式的兼容API密钥（使用随机盐加密）
     * 格式：sk-{spring_crypto_encrypted_data}
     *
     * @param userId 用户ID
     * @param keyName 密钥名称
     * @return 兼容格式的API密钥和加密数据
     */
    public CompatibleKeyResult generateCompatibleApiKey(Long userId, String keyName) {
        try {
            if (userId == null || userId <= 0) {
                throw new IllegalArgumentException("用户ID不能为空或小于等于0");
            }
            if (keyName == null || keyName.trim().isEmpty()) {
                keyName = "default";
            }

            // 1. 生成随机盐
            String randomSalt = generateRandomSalt();

            // 2. 构造明文数据：userId|timestamp|keyName|random
            String plaintext = String.format("%d|%d|%s|%d",
                userId,
                System.currentTimeMillis(),
                keyName.trim(),
                ThreadLocalRandom.current().nextLong()
            );

            // 3. 使用随机盐创建加密器并加密，加密器内部使用密码加盐能生成AES的32字节密钥来加密或解密
            BytesEncryptor encryptor = Encryptors.stronger(encryptionPassword, randomSalt);
            byte[] encryptedBytes = encryptor.encrypt(plaintext.getBytes(StandardCharsets.UTF_8));
            String encrypted = Base64.getUrlEncoder().withoutPadding().encodeToString(encryptedBytes);
            String compatibleKey = "sk-" + encrypted;

            // 4. 生成密钥哈希用于标识
            String keyHash = generateHash(compatibleKey);


            log.debug("生成兼容API密钥成功 - 用户ID: {}, 密钥名称: {}, 密钥哈希: {}", userId, keyName, keyHash);
            return new CompatibleKeyResult(compatibleKey, keyHash, randomSalt);
        } catch (Exception e) {
            log.error("生成兼容API密钥失败 - 用户ID: {}, 密钥名称: {}", userId, keyName, e);
            throw new RuntimeException("生成兼容API密钥失败: " + e.getMessage(), e);
        }
    }



    /**
     * 解析兼容API密钥（使用提供的盐值进行解密）
     * 从密钥中提取用户信息，用于负载均衡
     *
     * @param compatibleKey 兼容密钥
     * @param salt 盐值
     * @return 解析结果
     */
    public ParseResultV2 parseCompatibleApiKey(String compatibleKey, String salt) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-")) {
                return new ParseResultV2(false, null, null, null, "密钥格式无效");
            }

            if (salt == null || salt.trim().isEmpty()) {
                return new ParseResultV2(false, null, null, null, "盐值不能为空");
            }

            // 1. 使用提供的盐值创建解密器
            BytesEncryptor encryptor = Encryptors.stronger(encryptionPassword, salt);

            // 2. 解密密钥内容
            String encrypted = compatibleKey.substring(3); // 去掉"sk-"
            byte[] encryptedBytes = Base64.getUrlDecoder().decode(encrypted);
            byte[] decryptedBytes = encryptor.decrypt(encryptedBytes);
            String plaintext = new String(decryptedBytes, StandardCharsets.UTF_8);
            String[] parts = plaintext.split("\\|");

            if (parts.length != 4) {
                return new ParseResultV2(false, null, null, null, "密钥数据格式无效");
            }

            Long userId = Long.parseLong(parts[0]);
            Long timestamp = Long.parseLong(parts[1]);
            String keyName = parts[2];

            // 3. 验证时间戳（密钥有效期：1年）
            long currentTime = System.currentTimeMillis();
            if (currentTime - timestamp > 365L * 24 * 60 * 60 * 1000) {
                return new ParseResultV2(false, userId, timestamp, keyName, "密钥已过期");
            }

            log.debug("解析兼容API密钥成功 - 用户ID: {}, 密钥名称: {}", userId, keyName);
            return new ParseResultV2(true, userId, timestamp, keyName, "解析成功");
        } catch (Exception e) {
            log.warn("解析兼容API密钥失败: {}", compatibleKey, e);
            return new ParseResultV2(false, null, null, null, "解析失败: " + e.getMessage());
        }
    }











    /**
     * 生成哈希值
     */
    private String generateHash(String data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes).replaceAll("[^a-zA-Z0-9]", "");
    }

    /**
     * 脱敏显示API密钥
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }

        if (apiKey.startsWith("sk-")) {
            // OpenAI格式密钥脱敏
            return apiKey.substring(0, 7) + "****" + apiKey.substring(apiKey.length() - 4);
        } else {
            // 其他格式密钥脱敏
            return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
        }
    }

    /**
     * 兼容密钥生成结果
     */
    public static class CompatibleKeyResult {
        private final String compatibleKey;
        private final String securityHash;
        private final String salt;

        public CompatibleKeyResult( String compatibleKey, String securityHash, String salt) {
            this.compatibleKey = compatibleKey;
            this.securityHash = securityHash;
            this.salt = salt;
        }

        public String getCompatibleKey() { return compatibleKey; }
        public String getSecurityHash() { return securityHash; }
        public String getSalt() { return salt; }
    }

    /**
     * 解析结果（支持密钥名称和错误信息）
     */
    public static class ParseResultV2 {
        private final boolean valid;
        private final Long userId;
        private final Long timestamp;
        private final String keyName;
        private final String message;

        public ParseResultV2(boolean valid, Long userId, Long timestamp, String keyName, String message) {
            this.valid = valid;
            this.userId = userId;
            this.timestamp = timestamp;
            this.keyName = keyName;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public Long getTimestamp() { return timestamp; }
        public String getKeyName() { return keyName; }
        public String getMessage() { return message; }
    }
}
